import { useCallback, useEffect, useState, useRef, useMemo } from 'react';
import { useStore } from 'zustand';
import {
  clearHistory,
  redo,
  undo,
  useImageStore,
  type ImageState,
} from '@/store/imageStore';
import { imageStorage } from '@/storage/indexeddbStorage';
import type { CanvasImageEditorHandles } from '../../components/background-remover/components/CanvasImageEditor';
import {
  calculateOptimalImageSize,
  cleanupImageDeletionState,
} from '@/lib/imageUtils/imageState';
import {
  MAX_ZOOM_SCALE,
  ZOOM_STEP,
  ERASE_HISTORY_LIMIT,
} from '@/config/constants';

export const useImageEditor = (
  processingImageIds?: Set<string>,
  onImageSelect?: (imageId: string) => Promise<void>
) => {
  // 从 Zustand store 获取状态
  const imagesMap = useImageStore(s => s.images);
  const selectedImageIds = useImageStore(s => s.selectedImageIds);
  const imageActions = useImageStore.getState();

  // 获取 temporal 状态
  const temporalState = useStore(useImageStore.temporal, state => state);

  // 在组件内部将 Map 转换为数组，以用于渲染
  // 按照图片的时间戳排序，确保最新上传的图片在最前面
  const images = useMemo(() => {
    return Array.from(imagesMap.values()).sort((a, b) => {
      // 使用图片的timestamp字段进行排序
      const aTime = a.timestamp || 0;
      const bTime = b.timestamp || 0;

      // 按时间戳降序排序（最新的在前）
      if (aTime !== bTime) {
        return bTime - aTime;
      }

      // 如果时间戳相同，按照图片名称排序
      return a.name.localeCompare(b.name);
    });
  }, [imagesMap]);

  // 当前正在编辑的图片 ID
  const currentImageId = selectedImageIds.values().next().value || null;

  // 当前图片的 Image 对象
  const [currentImageObject, setCurrentImageObject] =
    useState<HTMLImageElement | null>(null);

  // 缩放相关状态
  const [currentScale, setCurrentScale] = useState(1);
  const [initialScale, setInitialScale] = useState(1);

  // UI状态
  const [isCompareActive, setIsCompareActive] = useState(false);
  const [forceCloseEraserTool, setForceCloseEraserTool] = useState(false);
  const [forceOpenEraserTool, setForceOpenEraserTool] = useState(false);
  const [isHandToolActive, setIsHandToolActive] = useState(false);

  // Canvas 容器相关状态
  const [canvasWrapperStyle, setCanvasWrapperStyle] =
    useState<React.CSSProperties>({});
  const [wrapperSize, setWrapperSize] = useState({ width: 0, height: 0 });

  // 对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<string | null>(null);

  // Canvas 编辑器引用
  const canvasEditorRef = useRef<CanvasImageEditorHandles>(null);

  // 判断当前是否可以拖动（图片放大时才能拖动）
  const canPan = currentImageObject && currentScale > initialScale;

  // 当无法拖动时，自动取消手形工具激活状态
  useEffect(() => {
    if (!canPan && isHandToolActive) {
      setIsHandToolActive(false);
    }
  }, [canPan, isHandToolActive]);

  // 当图片列表为空时，确保重置UI状态
  useEffect(() => {
    if (imagesMap.size === 0) {
      setCurrentImageObject(null);
      setCanvasWrapperStyle({});
      setWrapperSize({ width: 0, height: 0 });
      setInitialScale(1);
      setCurrentScale(1);
    }
  }, [imagesMap]);

  // 当 currentImageId 变化时，加载对应的图片对象并计算合适的初始缩放和容器尺寸
  useEffect(() => {
    let isCancelled = false;

    if (!currentImageId) {
      setCurrentImageObject(null);
      setCanvasWrapperStyle({});
      setWrapperSize({ width: 0, height: 0 });
      setInitialScale(1);
      setCurrentScale(1);
      return;
    }

    const imageToLoad = useImageStore.getState().images.get(currentImageId);

    if (imageToLoad) {
      const img = new window.Image();
      img.onload = () => {
        if (isCancelled) return;

        const imgNaturalWidth = img.naturalWidth;
        const imgNaturalHeight = img.naturalHeight;

        const sizeCalculation = calculateOptimalImageSize(
          imgNaturalWidth,
          imgNaturalHeight,
          window.innerHeight
        );

        setCanvasWrapperStyle(sizeCalculation.canvasWrapperStyle);
        setWrapperSize(sizeCalculation.wrapperSize);
        setInitialScale(sizeCalculation.initialScale);
        setCurrentScale(sizeCalculation.initialScale);
        setCurrentImageObject(img);
      };

      img.onerror = () => {
        if (isCancelled) return;
        console.error('从历史记录加载图片对象失败。');
        setCurrentImageObject(null);
      };

      img.src = imageToLoad.previewUrl;
    }

    return () => {
      isCancelled = true;
    };
  }, [currentImageId]);

  // 从 imageHistory 中获取当前图片的状态
  const currentImageFromHistory = currentImageId
    ? useImageStore.getState().images.get(currentImageId)
    : undefined;

  /**
   * 更新当前图片的设置，并记录到操作历史中
   */
  const updateCurrentImageSettings = useCallback(
    (newSettings: Partial<Omit<ImageState, 'id' | 'file'>>) => {
      if (!currentImageId) return;

      // 如果激活了橡皮擦模式且当前图片有背景去除错误，则清除错误状态
      if (newSettings.isEraseMode || newSettings.isRestoreMode) {
        const currentImage = useImageStore
          .getState()
          .images.get(currentImageId);
        if (currentImage?.status === 'bg-remove-failed') {
          newSettings = {
            ...newSettings,
            status: 'original',
          };
        }
      }

      imageActions.updateImage(currentImageId, newSettings);
    },
    [currentImageId, imageActions]
  );

  /**
   * 撤销操作
   */
  const handleUndo = useCallback(() => {
    const temporalState = useImageStore.temporal.getState();

    // 检查撤销后是否会导致图片列表为空
    if (temporalState.pastStates.length > 0) {
      const lastPastState =
        temporalState.pastStates[temporalState.pastStates.length - 1];

      // 如果撤销后图片列表为空，则不执行撤销
      if (lastPastState?.images?.size === 0) {
        return;
      }
    }

    undo();
  }, []);

  /**
   * 重做操作
   */
  const handleRedo = redo;

  /**
   * 处理橡皮擦操作完成
   */
  const handleEraseOperationComplete = useCallback(
    (data: {
      historyData: ImageData | null;
      currentCanvasData: ImageData | null;
    }) => {
      if (!currentImageId) return;

      const currentImage = useImageStore.getState().images.get(currentImageId);
      if (!currentImage) return;

      // 准备更新的数据
      const updates: Partial<Omit<ImageState, 'id'>> = {
        currentEraseCanvasData: data.currentCanvasData,
        eraseOperationCount: currentImage.eraseOperationCount + 1,
      };

      // 总是更新橡皮擦历史记录，即使是第一次操作（空画布）
      // 这确保每次橡皮擦操作都会创建全局历史记录，使撤销功能正常工作
      if (data.historyData) {
        const newEraseHistory = [
          ...currentImage.eraseHistory,
          data.historyData,
        ];

        // 限制历史记录数量，防止内存过度使用
        if (newEraseHistory.length > ERASE_HISTORY_LIMIT) {
          newEraseHistory.splice(
            0,
            newEraseHistory.length - ERASE_HISTORY_LIMIT
          );
        }

        updates.eraseHistory = newEraseHistory;
      }

      // 一次性更新所有橡皮擦相关状态，总是创建一条全局历史记录
      imageActions.updateImage(currentImageId, updates);
    },
    [currentImageId, imageActions]
  );

  // 监听当前图片变化，恢复橡皮擦画布状态
  useEffect(() => {
    if (!canvasEditorRef.current || !currentImageObject) {
      return;
    }

    // 延迟恢复，确保canvas已经初始化
    const timeoutId = setTimeout(() => {
      if (canvasEditorRef.current) {
        if (currentImageFromHistory?.currentEraseCanvasData) {
          canvasEditorRef.current.restoreEraseCanvasData(
            currentImageFromHistory.currentEraseCanvasData
          );
        } else {
          // 如果没有橡皮擦数据，清空橡皮擦图层

          canvasEditorRef.current.clearEraseLayer();
        }
      }
    }, 150);

    return () => clearTimeout(timeoutId);
  }, [
    currentImageFromHistory?.currentEraseCanvasData,
    currentImageFromHistory?.eraseOperationCount,
    currentImageId,
    currentImageObject,
  ]);

  /**
   * 缩放相关函数
   */
  const zoomIn = useCallback(() => {
    const currentPercent = Math.round((currentScale / initialScale) * 100);
    const newPercent = Math.min(
      MAX_ZOOM_SCALE * 100,
      currentPercent + ZOOM_STEP
    );
    setCurrentScale((newPercent / 100) * initialScale);
  }, [currentScale, initialScale]);

  const zoomOut = useCallback(() => {
    const currentPercent = Math.round((currentScale / initialScale) * 100);
    const newPercent = Math.max(100, currentPercent - ZOOM_STEP);
    setCurrentScale((newPercent / 100) * initialScale);
  }, [currentScale, initialScale]);

  const resetZoom = useCallback(() => {
    setCurrentScale(initialScale);
  }, [initialScale]);

  /**
   * 手形工具切换
   */
  const toggleHandTool = useCallback(() => {
    const newHandToolState = !isHandToolActive;
    setIsHandToolActive(newHandToolState);

    // 当激活手形工具时，关闭橡皮擦功能
    if (newHandToolState && currentImageId) {
      const currentImage = useImageStore.getState().images.get(currentImageId);
      if (
        currentImage &&
        (currentImage.isEraseMode || currentImage.isRestoreMode)
      ) {
        updateCurrentImageSettings({
          isEraseMode: false,
          isRestoreMode: false,
        });
        // 强制关闭橡皮擦弹窗
        setForceCloseEraserTool(true);
        setTimeout(() => setForceCloseEraserTool(false), 100);
      }
    }
  }, [isHandToolActive, currentImageId, updateCurrentImageSettings]);

  /**
   * 处理下载事件
   */
  const handleDownload = useCallback(async () => {
    if (!currentImageObject || !canvasEditorRef.current) {
      console.error('下载失败：没有当前图像或编辑器引用。');
      return;
    }

    try {
      const imageDataUrl =
        await canvasEditorRef.current.getDownloadableImageData();
      if (imageDataUrl) {
        const link = document.createElement('a');
        const originalName = currentImageObject.name || 'image.png';

        // 直接使用原始文件名，不添加后缀
        const fileName = originalName.includes('.')
          ? originalName
          : `${originalName}.png`;

        link.href = imageDataUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      } else {
        console.error('下载失败：未能从编辑器获取图像数据。');
      }
    } catch (error) {
      console.error('下载过程中发生错误:', error);
    }
  }, [currentImageObject]);

  /**
   * 显示删除确认弹窗
   */
  const showDeleteConfirmation = useCallback((imageId: string) => {
    setImageToDelete(imageId);
    setDeleteDialogOpen(true);
  }, []);

  /**
   * 确认删除图片
   */
  const confirmDeleteImage = useCallback(async () => {
    if (!imageToDelete) return;
    try {
      await handleRemoveImage(imageToDelete);
    } catch (error) {
      console.error('删除图片失败:', error);
    } finally {
      setDeleteDialogOpen(false);
      setImageToDelete(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [imageToDelete]);

  /**
   * 取消删除
   */
  const cancelDeleteImage = useCallback(() => {
    setDeleteDialogOpen(false);
    setImageToDelete(null);
  }, []);

  /**
   * 从历史记录中移除一张图片
   * 删除操作不记录到历史中，因为删除是不可逆的
   */
  const handleRemoveImage = useCallback(
    async (imageId: string) => {
      // 暂停历史记录跟踪，删除操作不应该被记录
      useImageStore.temporal.getState().pause();

      try {
        // 获取要删除的图片信息，用于资源清理
        const imageToDelete = useImageStore.getState().images.get(imageId);

        // 使用工具函数检查删除状态并获取清理操作
        const isCurrentImage = currentImageId === imageId;
        const isProcessing = processingImageIds?.has(imageId) || false;

        const { shouldWarnUser, warningMessage, cleanupActions } =
          cleanupImageDeletionState(
            imageId,
            imageToDelete
              ? {
                  previewUrl: imageToDelete.previewUrl,
                  processedUrl: imageToDelete.processedUrl || undefined,
                  backgroundImageUrl: imageToDelete.backgroundImageUrl,
                }
              : undefined,
            isCurrentImage,
            isProcessing
          );

        // 如果需要警告用户
        if (shouldWarnUser && warningMessage) {
          console.warn(`删除图片 ${imageId}: ${warningMessage}`);
        }

        // 检查是否使用了自定义背景图片，并检查是否需要清理
        let backgroundImageToCheck: { url: string; id?: string } | undefined;
        if (imageToDelete?.backgroundImageUrl?.startsWith('blob:')) {
          backgroundImageToCheck = {
            url: imageToDelete.backgroundImageUrl,
            id: imageToDelete.backgroundImageId,
          };
        }

        // 执行所有清理操作
        cleanupActions.forEach(action => {
          try {
            action();
          } catch (error) {
            console.error('清理操作失败:', error);
          }
        });

        // 获取删除前的图片列表
        const imagesBeforeDelete = Array.from(
          useImageStore.getState().images.keys()
        );

        // 如果删除的是当前选中的图片，需要处理选择切换
        if (currentImageId === imageId) {
          const remainingImages = imagesBeforeDelete.filter(
            id => id !== imageId
          );

          // 强制关闭橡皮擦弹窗和重置相关状态
          setForceCloseEraserTool(true);
          setIsHandToolActive(false);

          if (remainingImages.length > 0) {
            // 选择下一张图片
            const nextImageId = remainingImages[0];

            // 如果提供了onImageSelect回调，使用它来处理图片选择
            // 这样可以触发自动去背逻辑（如果需要的话）
            if (onImageSelect) {
              // 先更新选择状态，然后异步处理图片选择逻辑
              useImageStore.setState(state => {
                state.selectedImageIds.clear();
                state.selectedImageIds.add(nextImageId);
              });

              // 异步调用选择处理逻辑，可能包含自动去背
              setTimeout(() => {
                onImageSelect(nextImageId).catch(error => {
                  console.error('处理删除后的图片选择失败:', error);
                });
              }, 100);
            } else {
              // 如果没有提供回调，直接切换到下一张图片（兼容模式）
              useImageStore.setState(state => {
                state.selectedImageIds.clear();
                state.selectedImageIds.add(nextImageId);
              });
            }
          } else {
            // 如果没有剩余图片，清空选择并重置所有UI状态
            useImageStore.setState(state => {
              state.selectedImageIds.clear();
            });

            // 重置所有相关的UI状态
            setCurrentImageObject(null);
            setCanvasWrapperStyle({});
            setWrapperSize({ width: 0, height: 0 });
            setInitialScale(1);
            setCurrentScale(1);
            setIsCompareActive(false);
          }

          // 清除全局历史记录，确保撤销/重做状态正确
          clearHistory();

          // 重置橡皮擦弹窗状态
          setTimeout(() => setForceCloseEraserTool(false), 100);
        }

        // 执行删除操作（这会自动删除IndexedDB中的图片数据）
        imageActions.removeImage(imageId);

        // 检查是否需要清理未使用的自定义背景图片
        if (backgroundImageToCheck) {
          // 短暂延迟确保删除操作完成
          setTimeout(async () => {
            try {
              await checkAndCleanupUnusedBackgroundImage(
                backgroundImageToCheck,
                imageId // 传递被删除的图片ID
              );
            } catch (error) {
              console.error('清理未使用的背景图片失败:', error);
            }
          }, 100); // 减少延迟时间
        }
      } finally {
        // 恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [currentImageId, imageActions, processingImageIds, onImageSelect]
  );

  /**
   * 检查并清理未使用的自定义背景图片
   */
  const checkAndCleanupUnusedBackgroundImage = useCallback(
    async (
      backgroundImage: { url: string; id?: string },
      deletedImageId?: string
    ) => {
      if (!backgroundImage.url.startsWith('blob:')) {
        return; // 只处理blob URL（自定义上传的图片）
      }

      // 预设背景图片不需要清理
      if (backgroundImage.id?.startsWith('preset-')) {
        return;
      }

      // 检查当前所有图片是否还有使用这个背景图片
      // 注意：需要排除正在删除的图片
      const allImages = Array.from(
        useImageStore.getState().images.values()
      ).filter(img => img.id !== deletedImageId);

      // 检查是否还有图片使用这个背景图片
      const stillInUse = allImages.some(
        img =>
          img.backgroundImageUrl === backgroundImage.url ||
          (backgroundImage.id && img.backgroundImageId === backgroundImage.id)
      );

      if (stillInUse) {
        return; // 如果仍在使用，直接返回，不执行任何清理操作
      }

      // 如果是自定义上传的背景图片，通知主组件清理
      try {
        window.dispatchEvent(
          new CustomEvent('cleanupBackgroundImage', {
            detail: {
              url: backgroundImage.url,
              id: backgroundImage.id,
            },
          })
        );
      } catch (error) {
        console.error('通知清理背景图片失败:', error);
      }
    },
    []
  );

  /**
   * 在历史记录中选择一张图片进行编辑
   */
  const handleSelectImage = useCallback(async (imageId: string) => {
    // 切换图片时关闭橡皮擦弹窗
    setForceCloseEraserTool(true);

    // 切换图片时取消手形工具激活状态
    setIsHandToolActive(false);

    // 使用一次 setState 调用来避免创建多条历史记录
    useImageStore.setState(state => {
      state.selectedImageIds.clear();
      state.selectedImageIds.add(imageId);
    });

    // 保存当前选中的图片ID到持久化存储
    const currentState = useImageStore.getState();
    if (currentState.isStorageInitialized) {
      try {
        await imageStorage.saveCurrentSelectedImageId(imageId);
      } catch (error) {
        console.error('保存选中图片ID失败:', error);
      }
    }

    // 切换图片时清除全局历史记录，确保撤销/重做只针对当前图片
    clearHistory();

    // 重置强制关闭状态
    setTimeout(() => setForceCloseEraserTool(false), 100);

    // 检查所选图片是否为 original 状态，如果是则需要外部处理
    const selectedImage = useImageStore.getState().images.get(imageId);
    return selectedImage;
  }, []);

  /**
   * 触发橡皮擦工具打开
   */
  const triggerEraserToolOpen = useCallback(() => {
    setForceOpenEraserTool(true);
    setTimeout(() => setForceOpenEraserTool(false), 100);
  }, []);

  return {
    // 状态
    images,
    currentImageId,
    currentImageObject,
    currentImageFromHistory,
    temporalState,

    // 缩放相关
    currentScale,
    initialScale,
    canPan,

    // UI状态
    isCompareActive,
    setIsCompareActive,
    forceCloseEraserTool,
    forceOpenEraserTool,
    isHandToolActive,

    // Canvas相关
    canvasWrapperStyle,
    wrapperSize,
    canvasEditorRef,

    // 对话框状态
    deleteDialogOpen,
    setDeleteDialogOpen,
    imageToDelete,

    // 函数
    updateCurrentImageSettings,
    handleUndo,
    handleRedo,
    handleEraseOperationComplete,
    zoomIn,
    zoomOut,
    resetZoom,
    toggleHandTool,
    handleDownload,
    showDeleteConfirmation,
    confirmDeleteImage,
    cancelDeleteImage,
    handleRemoveImage,
    handleSelectImage,
    triggerEraserToolOpen,
  };
};
