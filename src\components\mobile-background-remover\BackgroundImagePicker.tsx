'use client';

import { Button } from '@/components/ui/Button';
import {
  Drawer,
  Drawer<PERSON>ontent,
  DrawerHeader,
  Drawer<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from '@/components/ui/Drawer';
import { ChevronLeft, ChevronRight, Upload, X } from 'lucide-react';
import Image from 'next/image';
import { useRef, useState } from 'react';

/**
 * 代表一个已上传的图片。
 */
export interface UploadedImage {
  id: string;
  url: string; // 用于显示的 Object URL
  name: string;
  timestamp: number;
  file?: File; // 原始文件，可选
}

/**
 * 背景图片选择器组件的 Props。
 */
interface BackgroundImagePickerProps {
  currentBackgroundImageUrl: string | undefined;
  onSelectImage: (url: string | undefined) => void;
  onFileUpload: (file: File) => void; // For new uploads
  uploadedImages: UploadedImage[];
  children: React.ReactNode; // For the trigger button
}

/**
 * 预设图片。
 */
interface PresetImage {
  id: string;
  name: string;
  url: string;
}

/**
 * 预设图片分类。
 */
interface PresetCategory {
  id: string;
  name: string;
  images: PresetImage[];
}

/**
 * 背景图片分类配置
 */
const BACKGROUND_CATEGORIES_CONFIG = {
  gradient: { name: '渐变', imageCount: 6 },
  landscape: { name: '风景', imageCount: 6 },
  geometry: { name: '几何', imageCount: 6 },
  grain: { name: '木纹', imageCount: 6 },
  paper: { name: '纸张', imageCount: 6 },
  texture: { name: '纹理', imageCount: 6 },
} as const;

/**
 * 动态生成预设图片分类
 */
const generatePresetCategories = (): PresetCategory[] => {
  return Object.entries(BACKGROUND_CATEGORIES_CONFIG).map(
    ([categoryId, config]) => ({
      id: categoryId,
      name: config.name,
      images: Array.from({ length: config.imageCount }, (_, index) => ({
        id: `${categoryId}${index + 1}`,
        name: `${config.name}${index + 1}`,
        url: `/apps/images/background/${categoryId}/${index + 1}.png`,
      })),
    })
  );
};

// 动态生成的预设分类
const PRESET_CATEGORIES: PresetCategory[] = generatePresetCategories();

/**
 * 一个允许用户选择预设背景图片、上传自定义图片或选择透明背景的组件。
 */
export function MobileBackgroundImagePicker({
  currentBackgroundImageUrl,
  onSelectImage,
  onFileUpload,
  uploadedImages,
  children,
}: BackgroundImagePickerProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [categoryPages, setCategoryPages] = useState<Record<string, number>>(
    {}
  );
  const [uploadedImagesPage, setUploadedImagesPage] = useState(0);
  const [open, setOpen] = useState(false);

  const getCategoryPage = (categoryId: string) =>
    categoryPages[categoryId] || 0;

  const setCategoryPage = (categoryId: string, page: number) => {
    setCategoryPages(prev => ({
      ...prev,
      [categoryId]: page,
    }));
  };

  // 按时间戳降序排序上传的图片（最新的在前）
  const sortedUploadedImages = [...uploadedImages].sort(
    (a, b) => b.timestamp - a.timestamp
  );

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileUpload(file);
      // 重置文件输入，以便可以再次上传同一个文件
      if (fileInputRef.current) fileInputRef.current.value = '';
    }
  };

  const handleClosePopover = () => {
    setOpen(false);
  };

  // 透明背景的棋盘图案样式
  const checkerboardStyle = {
    backgroundImage: `
      linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)
    `,
    backgroundSize: '8px 8px',
    backgroundPosition: '0 0, 0 4px, 4px -4px, -4px 0px',
  };

  return (
    <Drawer open={open} onOpenChange={setOpen} modal={false}>
      <DrawerTrigger asChild>{children}</DrawerTrigger>
      <DrawerContent className='p-6 rounded-t-2xl border border-[#E7E7E7] shadow-[0px_7px_14px_0px_#DCDFE429,0px_8px_16px_0px_#DCDFE41F,0px_10px_32px_0px_#DCDFE414] data-[vaul-drawer-direction=bottom]:mt-0'>
        <DrawerHeader className='flex justify-between mb-4 p-0 -mt-1'>
          <DrawerTitle className='text-[16px] font-bold text-[#121212] text-left'>
            Change Background Images
          </DrawerTitle>
          {/* 右上角关闭按钮 */}
          <button
            onClick={handleClosePopover}
            className='absolute top-5 right-5 w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors cursor-pointer'
          >
            <X className='w-6 h-6 text-[#121212]' strokeWidth={1.5} />
          </button>
        </DrawerHeader>
        <div className='h-px bg-[#E7E7E7] mb-4' />

        <div className='max-h-67 overflow-auto'>
          {/* 透明背景和上传区域 */}
          <div className='space-y-4'>
            {/* 图片滑动容器 */}
            <div className='relative'>
              {(() => {
                const itemsPerPage = 3;
                // 计算总项目数：透明背景 + 上传按钮 + 所有上传图片
                const totalItems = 2 + sortedUploadedImages.length;
                // 计算总页数
                const totalPages = Math.ceil(totalItems / itemsPerPage);
                const canGoLeft = uploadedImagesPage > 0;
                const canGoRight = uploadedImagesPage < totalPages - 1;

                return (
                  <>
                    {/* 左箭头按钮 */}
                    {canGoLeft && (
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() =>
                          setUploadedImagesPage(uploadedImagesPage - 1)
                        }
                        className='absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 h-8 w-8 rounded-full bg-white/80 hover:bg-white shadow-md border border-gray-200 cursor-pointer'
                      >
                        <ChevronLeft className='w-4 h-4' />
                      </Button>
                    )}

                    {/* 右箭头按钮 */}
                    {canGoRight && (
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() =>
                          setUploadedImagesPage(uploadedImagesPage + 1)
                        }
                        className='absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 h-8 w-8 rounded-full bg-white/80 hover:bg-white shadow-md border border-gray-200 cursor-pointer'
                      >
                        <ChevronRight className='w-4 h-4' />
                      </Button>
                    )}

                    {/* 图片滑动区域 */}
                    <div className='overflow-hidden rounded-lg'>
                      <div
                        className='flex gap-3 transition-transform duration-300 ease-in-out'
                        style={{
                          transform: `translateX(-${uploadedImagesPage * (3 * 96 + 2 * 12)}px)`, // 3张图片宽度 + 2个间距
                        }}
                      >
                        {/* 透明背景按钮 */}
                        <Button
                          variant='outline'
                          className={`h-24 w-24 p-0 border-3 rounded-lg flex-shrink-0 ${
                            currentBackgroundImageUrl === undefined
                              ? 'border-[#FFD700] ring-0'
                              : 'border-transparent hover:border-gray-300'
                          }`}
                          onClick={() => onSelectImage(undefined)}
                          style={checkerboardStyle}
                        >
                          <div
                            className='w-full h-full rounded-lg'
                            style={checkerboardStyle}
                          />
                        </Button>

                        {/* 上传按钮 */}
                        <Button
                          variant='outline'
                          className='h-24 w-24 flex flex-col items-center justify-center p-2 space-y-1 border-2 border-gray-200 rounded-lg hover:border-gray-300 transition-colors flex-shrink-0'
                          onClick={handleUploadClick}
                        >
                          <Upload className='w-5 h-5 text-gray-600' />
                          <span className='text-sm text-gray-600'>Upload</span>
                          <input
                            type='file'
                            ref={fileInputRef}
                            onChange={handleFileChange}
                            accept='image/*'
                            className='hidden'
                          />
                        </Button>

                        {/* 所有上传的图片 */}
                        {sortedUploadedImages.map(img => (
                          <Button
                            key={img.id}
                            variant='outline'
                            className={`h-24 w-24 p-0 relative border-3 group overflow-hidden transition-all duration-300 ease-in-out rounded-lg flex-shrink-0 ${
                              currentBackgroundImageUrl === img.url
                                ? 'border-[#FFD700] ring-0'
                                : 'border-transparent hover:border-gray-300'
                            }`}
                            onClick={() => onSelectImage(img.url)}
                          >
                            <Image
                              src={img.url}
                              alt={img.name}
                              className='w-full h-full object-cover transition-opacity duration-300 ease-in-out group-hover:opacity-90 will-change-auto rounded-lg'
                              width={96}
                              height={96}
                            />
                          </Button>
                        ))}
                      </div>
                    </div>
                  </>
                );
              })()}
            </div>
          </div>

          {/* 预设分类区域 */}
          <div className='space-y-4'>
            {PRESET_CATEGORIES.map(category => {
              const currentPage = getCategoryPage(category.id);
              const itemsPerPage = 3;
              const totalPages = Math.ceil(
                category.images.length / itemsPerPage
              );
              const canGoLeft = currentPage > 0;
              const canGoRight = currentPage < totalPages - 1;

              return (
                <div key={category.id} className='space-y-2 mt-4'>
                  <div className='flex justify-between items-center'>
                    <p className='text-sm font-medium text-[#878787]'>
                      {category.name}
                    </p>
                  </div>

                  {/* 图片滑动容器 */}
                  <div className='relative'>
                    {/* 左箭头按钮 */}
                    {canGoLeft && (
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() =>
                          setCategoryPage(category.id, currentPage - 1)
                        }
                        className='absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 h-8 w-8 rounded-full bg-white/80 hover:bg-white shadow-md border border-gray-200'
                      >
                        <ChevronLeft className='w-4 h-4' />
                      </Button>
                    )}

                    {/* 右箭头按钮 */}
                    {canGoRight && (
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() =>
                          setCategoryPage(category.id, currentPage + 1)
                        }
                        className='absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 h-8 w-8 rounded-full bg-white/80 hover:bg-white shadow-md border border-gray-200'
                      >
                        <ChevronRight className='w-4 h-4' />
                      </Button>
                    )}

                    {/* 图片滑动区域 */}
                    <div className='overflow-hidden rounded-lg'>
                      <div
                        className='flex gap-3 transition-transform duration-300 ease-in-out'
                        style={{
                          transform: `translateX(-${currentPage * (3 * 96 + 2 * 12)}px)`, // 3张图片宽度 + 2个间距
                        }}
                      >
                        {category.images.map(img => (
                          <Button
                            key={img.id}
                            variant='outline'
                            className={`h-24 w-24 p-0 border-3 relative flex-shrink-0 group overflow-hidden transition-all duration-300 ease-in-out rounded-lg ${
                              currentBackgroundImageUrl === img.url
                                ? 'border-[#FFD700] ring-0'
                                : 'border-transparent hover:border-gray-300'
                            }`}
                            onClick={() => onSelectImage(img.url)}
                          >
                            <Image
                              src={img.url}
                              alt={img.name}
                              className='w-full h-full object-cover transition-opacity duration-300 ease-in-out group-hover:opacity-90 will-change-auto rounded-lg'
                              crossOrigin='anonymous'
                              width={96}
                              height={96}
                            />
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
