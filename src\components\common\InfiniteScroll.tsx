'use client';
import { useEffect, useRef } from 'react';

export function InfiniteScrollList({
  children,
  onLoadMore,
  isLoading,
  hasMore,
}: {
  children: React.ReactNode;
  onLoadMore: () => void;
  isLoading: boolean;
  hasMore: boolean;
}) {
  const sentinelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!sentinelRef.current || !hasMore) return;

    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && !isLoading) {
          onLoadMore();
        }
      },
      { threshold: 1.0 }
    );

    observer.observe(sentinelRef.current);

    return () => observer.disconnect();
  }, [isLoading, hasMore]);

  return (
    <div className='h-100 space-y-4 overflow-auto'>
      {children}
      <div ref={sentinelRef} className='h-1 w-full' />
      {isLoading && <div className='text-center py-4'>Loading...</div>}
      {!hasMore && <div className='text-center py-4'>No more data</div>}
    </div>
  );
}
