'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/base';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/Tabs';
import Image from 'next/image';
import { useTips } from '@/components/ui/Tips';
import { shouldDisableBatchApplyButton } from '../../../lib/buttonUtils';
import type { CompressionLevel } from '@/lib/imageUtils/imageCompress';

interface CompressSettings {
  level?: CompressionLevel;
  customSize?: number;
  customUnit?: 'KB' | 'MB';
}

interface CompressSettingsProps {
  onApply?: (settings: CompressSettings) => void;
  images?: Array<{
    id: string;
    status: string;
    originalSize?: number;
    compressedSize?: number;
    size: number;
  }>;
  isProcessing?: boolean;
}

// 压缩程度选项
const COMPRESSION_LEVELS = [
  {
    id: 'original',
    name: '原始',
    description: '恢复初始值',
    quality: '100%',
  },
  {
    id: 'light',
    name: '轻度（最清晰）',
    description: '轻微压缩，保持高画质',
    quality: '90%',
  },
  {
    id: 'medium',
    name: '中度（品质均衡）',
    description: '平衡文件大小和画质',
    quality: '70%',
  },
  {
    id: 'deep',
    name: '深度（最小体积）',
    description: '最大压缩，最小文件',
    quality: '50%',
  },
] as const;

export function CompressSettings({
  onApply,
  images = [],
  isProcessing = false,
}: CompressSettingsProps) {
  const { showTips } = useTips();
  const [compressionMode, setCompressionMode] = useState<'level' | 'custom'>(
    'level'
  );
  const [selectedLevel, setSelectedLevel] =
    useState<CompressionLevel>('medium');
  const [customSize, setCustomSize] = useState<string>('');
  const [customUnit, setCustomUnit] = useState<'KB' | 'MB'>('KB');

  const handleApply = () => {
    if (!onApply) return;

    if (compressionMode === 'level') {
      // 按压缩程度
      onApply({
        level: selectedLevel,
      });
    } else {
      // 自定义体积
      const sizeNum = parseFloat(customSize);

      if (isNaN(sizeNum) || sizeNum <= 0) {
        showTips('error', 'Please enter a valid file size.');
        return;
      }

      // 验证大小范围
      const sizeInKB = customUnit === 'MB' ? sizeNum * 1024 : sizeNum;
      if (sizeInKB < 1) {
        showTips('error', 'File size must be at least 1 KB.');
        return;
      }
      if (sizeInKB > 10240) {
        // 10MB
        showTips('error', 'File size cannot exceed 10 MB.');
        return;
      }

      onApply({
        customSize: sizeNum,
        customUnit,
      });
    }
  };

  return (
    <div className='w-[344px] bg-white border-r border-[#E7E7E7] flex flex-col h-full'>
      {/* 头部标题区域 */}
      <div className='pt-6 px-4 mb-4'>
        <div className='border-b border-border pb-4'>
          <h2 className='text-base font-bold'>Compress</h2>
        </div>
      </div>

      {/* 压缩模式选择和内容区域 */}
      <div className='flex-1 flex flex-col'>
        <Tabs
          value={compressionMode}
          onValueChange={value =>
            setCompressionMode(value as 'level' | 'custom')
          }
        >
          {/* Tab 选择器 */}
          <div className='px-4'>
            <TabsList className='w-full h-auto bg-border p-0.5'>
              <TabsTrigger
                value='level'
                className='data-[state=inactive]:text-[#878787] cursor-pointer'
              >
                按压缩程度
              </TabsTrigger>
              <TabsTrigger
                value='custom'
                className='data-[state=inactive]:text-[#878787] cursor-pointer'
              >
                自定义体积
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Tab 内容区域 */}
          <div className='flex-1 px-4'>
            <TabsContent value='level' className='space-y-2 mt-2 h-full'>
              {/* 按压缩程度 */}
              {COMPRESSION_LEVELS.map(level => (
                <button
                  key={level.id}
                  onClick={() => setSelectedLevel(level.id as CompressionLevel)}
                  className={cn(
                    'w-full p-3 bg-white rounded-lg border text-left transition-colors cursor-pointer',
                    selectedLevel === level.id
                      ? 'border-[#FFCC03] bg-[#FFFBF0]'
                      : 'border-[#E7E7E7] hover:bg-gray-50'
                  )}
                >
                  <div className='flex items-center justify-between'>
                    <div className='flex-1'>
                      <div className='text-sm font-medium text-[#121212]'>
                        {level.name}
                      </div>
                      <div className='text-xs text-[#878787] mt-1'>
                        {level.description}
                      </div>
                    </div>
                    <div className='text-xs text-[#878787] ml-2'>
                      {level.quality}
                    </div>
                  </div>
                </button>
              ))}
            </TabsContent>

            <TabsContent value='custom' className='space-y-4 mt-2 h-full'>
              {/* 自定义体积 */}
              <div className='text-sm text-[#121212] font-medium'>
                设置每张均小于
              </div>

              <div className='flex items-center gap-2'>
                <div className='flex-1'>
                  <Input
                    type='number'
                    value={customSize}
                    onChange={e => {
                      const value = e.target.value;
                      if (value === '' || /^\d*\.?\d*$/.test(value)) {
                        setCustomSize(value);
                      }
                    }}
                    placeholder='输入大小'
                    className='h-10 text-sm border-[#E7E7E7] rounded-lg'
                    min='0.1'
                    step='0.1'
                  />
                </div>

                <Select
                  value={customUnit}
                  onValueChange={(value: 'KB' | 'MB') => setCustomUnit(value)}
                >
                  <SelectTrigger className='w-20 h-10 bg-white border border-[#E7E7E7] rounded-lg px-3'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className='bg-white border border-[#E7E7E7] rounded-xl shadow-lg py-2 px-2'>
                    <SelectItem value='KB' className='h-8 text-sm'>
                      KB
                    </SelectItem>
                    <SelectItem value='MB' className='h-8 text-sm'>
                      MB
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='text-xs text-[#878787]'>
                支持范围：1 KB - 10 MB
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* 底部应用按钮 */}
      <div className='px-4 py-6 bg-white rounded-b-2xl'>
        <Button
          onClick={handleApply}
          disabled={shouldDisableBatchApplyButton(isProcessing, images)}
          className='w-full h-12 bg-[#FFCC03] hover:bg-[#FFCC03]/90 text-[#121212] text-base font-medium rounded-xl disabled:opacity-50'
          size='lg'
        >
          {isProcessing ? (
            <div className='flex items-center gap-2'>
              <Image
                src='/apps/icons/loading.png'
                alt='loading'
                width={16}
                height={16}
                className='animate-spin'
              />
              Processing...
            </div>
          ) : (
            'Apply'
          )}
        </Button>
      </div>
    </div>
  );
}
