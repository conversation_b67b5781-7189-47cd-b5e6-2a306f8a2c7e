import { ErrorBoundary } from '@/components/error-boundary';
import { TipsProvider } from '@/components/ui/Tips';
import { DeviceProvider } from '@/context/DeviceContext';
import { AuthProvider } from '@/components/auth/AuthProvider';
import { AuthGuard } from '@/components/auth/AuthGuard';
import { getDeviceType } from '@/lib/device';
import type { Metadata } from 'next';
import type { Viewport } from 'next';
import localFont from 'next/font/local';
import { headers } from 'next/headers';
import './globals.css';

// 配置 HarmonyOS Sans 本地字体
const harmonyOSSans = localFont({
  variable: '--font-harmonyos-sans',
  src: [
    {
      path: '../../public/fonts/HarmonyOS_Sans_Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/HarmonyOS_Sans_Medium.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/HarmonyOS_Sans_Bold.ttf',
      weight: '700',
      style: 'normal',
    },
  ],
  fallback: [
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'sans-serif',
  ],
  display: 'swap',
});

// 配置等宽字体变量（用于代码显示，使用系统默认等宽字体）
const geistMono = {
  variable: '--font-geist-mono',
};

/**
 * 应用的元数据，用于 SEO 和浏览器标签显示。
 */
export const metadata: Metadata = {
  title: 'AI 背景去除工具 - 智能图片背景处理',
  description:
    '使用先进的AI技术，一键去除图片背景，支持更换背景、调整颜色、添加阴影等专业编辑功能',
  keywords: ['AI', '背景去除', '图片编辑', 'remove.bg', 'fotor', '背景替换'],
  authors: [{ name: 'AI Background Remover' }],
};

/**
 * 应用的视口配置，用于控制页面在不同设备上的显示。
 * 移动端使用宽度优先，桌面端使用高度优先。
 */
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1.0,
  minimumScale: 1.0,
  viewportFit: 'cover',
  userScalable: false,
};

/**
 * 根布局组件，包裹整个应用。
 * 集成了设备检测功能，为移动端和桌面端提供不同的用户体验。
 * 使用 HarmonyOS Sans 作为主要字体，提供现代化的中文和英文显示效果。
 * @param {object} props - 组件 props
 * @param {React.ReactNode} props.children - 子组件
 * @returns {JSX.Element}
 */
export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // 获取请求头信息，用于服务端设备检测
  const headersList = await headers();
  const userAgent = headersList.get('user-agent') || '';

  // 服务端设备检测
  const initialDeviceType = getDeviceType(userAgent);

  return (
    <html lang='en' style={{ touchAction: 'none' }}>
      <body
        className={`${harmonyOSSans.variable} ${geistMono.variable} antialiased`}
      >
        <TipsProvider>
          <DeviceProvider initialDeviceType={initialDeviceType}>
            <ErrorBoundary>
              <AuthProvider>
                <AuthGuard>{children}</AuthGuard>
              </AuthProvider>
            </ErrorBoundary>
          </DeviceProvider>
        </TipsProvider>
      </body>
    </html>
  );
}
