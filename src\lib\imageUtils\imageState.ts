import { type ImageState } from '@/store/imageStore';
import {
  SUPPORTED_IMAGE_TYPES,
  SUPPORTED_FILE_EXTENSIONS,
  MAX_FILE_SIZE,
  MIN_DISPLAY_WIDTH,
  MIN_DISPLAY_HEIGHT,
  MAX_CANVAS_WIDTH,
} from '@/config/constants';

/**
 * 验证文件格式和大小是否支持
 */
export const validateImageFormat = (
  file: File
): { isValid: boolean; error?: string } => {
  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return { isValid: false, error: 'file-too-large' };
  }

  // 检查 MIME 类型
  if (!SUPPORTED_IMAGE_TYPES.includes(file.type.toLowerCase())) {
    return { isValid: false, error: 'invalid-file-type' };
  }

  // 检查文件扩展名
  const fileExtension = file.name
    .toLowerCase()
    .substring(file.name.lastIndexOf('.'));
  if (!SUPPORTED_FILE_EXTENSIONS.includes(fileExtension)) {
    return { isValid: false, error: 'invalid-file-type' };
  }

  return { isValid: true };
};

/**
 * 从URL或文件创建图片状态对象
 */
export const createImageFromSource = async (
  source: File | string,
  name?: string
): Promise<ImageState> => {
  return new Promise<ImageState>((resolve, reject) => {
    const img = new window.Image();
    img.crossOrigin = 'anonymous'; // 允许跨域加载图片

    img.onload = async () => {
      //   此处兼容没有randomUUID的旧版浏览器
      const id =
        crypto.randomUUID?.() ||
        '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, c =>
          (
            +c ^
            (crypto.getRandomValues(new Uint8Array(1))[0] &
              (15 >> (Number(c) / 4)))
          ).toString(16)
        );
      if (!id) {
        return reject(new Error('Failed to generate UUID for image.'));
      }

      let file: File | undefined;
      let previewUrl: string;
      let imageName: string;
      let size: number = 0;

      if (source instanceof File) {
        // 处理文件上传
        file = source;
        previewUrl = URL.createObjectURL(source);
        imageName = source.name;
        size = source.size;
      } else {
        // 处理URL
        previewUrl = source;
        imageName = name || `image_${Date.now()}.jpg`;
        // 对于URL，我们无法获取准确的文件大小，设置为0
        size = 0;
      }

      const newImage: ImageState = {
        id,
        file,
        previewUrl,
        name: imageName,
        width: img.naturalWidth,
        height: img.naturalHeight,
        size,
        status: 'original',
        history: [],
        timestamp: Date.now(), // 添加时间戳用于排序
        backgroundColor: 'transparent',
        backgroundImageUrl: undefined,
        isBlurEnabled: false,
        blurAmount: 0,
        processedUrl: null,
        // 橡皮擦相关默认值
        isEraseMode: false,
        isRestoreMode: false,
        eraseBrushSize: 80,
        eraseHistory: [],
        currentEraseCanvasData: null,
        eraseOperationCount: 0,
        // 尺寸调整相关默认值
        resizeMode: 'fit',
        targetWidth: undefined,
        targetHeight: undefined,
        originalWidth: undefined,
        originalHeight: undefined,
        resizedUrl: null,
        // 设置原始大小，这是真正的原始文件大小
        originalSize: size,
      };
      resolve(newImage);
    };

    img.onerror = () => reject(new Error('Image failed to load.'));

    if (source instanceof File) {
      const reader = new FileReader();
      reader.onload = () => {
        img.src = reader.result as string;
      };
      reader.onerror = () => reject(new Error('File reader failed.'));
      reader.readAsDataURL(source);
    } else {
      img.src = source;
    }
  });
};

/**
 * 计算图片的最佳显示尺寸和缩放比例
 */
export const calculateOptimalImageSize = (
  imgNaturalWidth: number,
  imgNaturalHeight: number,
  windowHeight: number
) => {
  if (imgNaturalWidth === 0 || imgNaturalHeight === 0 || windowHeight === 0) {
    return {
      canvasWrapperStyle: {},
      wrapperSize: { width: 0, height: 0 },
      initialScale: 1,
    };
  }

  // 计算 canvas 的最佳显示尺寸，应用智能缩放逻辑
  const aspectRatio = imgNaturalWidth / imgNaturalHeight;

  // 计算可用的最大宽度（PC端限制为828px）
  const maxAvailableWidth = MAX_CANVAS_WIDTH;

  // 计算可用的最大高度
  const maxAvailableHeight = Math.max(50, windowHeight - 396);

  // 设置最小显示尺寸（确保小图能充分利用空间）
  const minDisplayWidth = Math.min(maxAvailableWidth, MIN_DISPLAY_WIDTH); // PC端最小宽度400px
  const minDisplayHeight = MIN_DISPLAY_HEIGHT;

  // 计算不同约束下的缩放比例
  const scaleByWidth = maxAvailableWidth / imgNaturalWidth;
  const scaleByHeight = maxAvailableHeight / imgNaturalHeight;
  const scaleByMinWidth = minDisplayWidth / imgNaturalWidth;

  // 选择合适的缩放比例
  let finalScale: number;

  // 如果图片很小，需要放大到最小宽度
  if (imgNaturalWidth < minDisplayWidth) {
    // 检查按最小宽度放大后，高度是否超出
    const heightAfterMinWidthScale = imgNaturalHeight * scaleByMinWidth;
    if (heightAfterMinWidthScale <= maxAvailableHeight) {
      // 高度不超出，使用最小宽度缩放
      finalScale = scaleByMinWidth;
    } else {
      // 高度超出，使用高度约束（确保图片完全显示）
      finalScale = scaleByHeight;
    }
  } else {
    // 图片不小，选择更严格的约束（确保超宽、超高图片都能完全显示）
    finalScale = Math.min(scaleByWidth, scaleByHeight);
  }

  // 计算最终尺寸
  const targetCanvasWidth = Math.max(
    minDisplayHeight * aspectRatio, // 确保不会太小
    imgNaturalWidth * finalScale
  );
  const targetCanvasHeight = Math.max(
    minDisplayHeight,
    imgNaturalHeight * finalScale
  );

  // 最终确保不超过最大限制
  const finalCanvasWidth = Math.min(targetCanvasWidth, maxAvailableWidth);
  const finalCanvasHeight = Math.min(targetCanvasHeight, maxAvailableHeight);

  const newInitialScale = finalCanvasWidth / imgNaturalWidth;

  return {
    canvasWrapperStyle: {
      width: `${finalCanvasWidth}px`,
      height: `${finalCanvasHeight}px`,
    },
    wrapperSize: {
      width: finalCanvasWidth,
      height: finalCanvasHeight,
    },
    initialScale: newInitialScale,
  };
};

/**
 * 清理删除图片时可能遗留的状态和资源
 * @param imageId 要删除的图片ID
 * @param imageData 图片数据（用于资源清理）
 * @param isCurrentImage 是否为当前选中的图片
 * @param isProcessing 是否正在处理中
 */
export function cleanupImageDeletionState(
  imageId: string,
  imageData?: {
    previewUrl?: string;
    processedUrl?: string;
    backgroundImageUrl?: string;
  },
  isCurrentImage: boolean = false,
  isProcessing: boolean = false
): {
  shouldWarnUser: boolean;
  warningMessage?: string;
  cleanupActions: (() => void)[];
} {
  const cleanupActions: (() => void)[] = [];
  let shouldWarnUser = false;
  let warningMessage: string | undefined;

  // 检查是否正在处理中
  if (isProcessing) {
    shouldWarnUser = true;
    warningMessage = '该图片正在后台处理中，删除将取消处理操作。';
  }

  // 清理 blob URLs
  if (imageData?.previewUrl?.startsWith('blob:')) {
    cleanupActions.push(() => URL.revokeObjectURL(imageData.previewUrl!));
  }

  if (imageData?.processedUrl?.startsWith('blob:')) {
    cleanupActions.push(() => URL.revokeObjectURL(imageData.processedUrl!));
  }

  // 注意：不在这里清理 backgroundImageUrl，因为背景图片可能被多张图片共享
  // 背景图片的清理由专门的 checkAndCleanupUnusedBackgroundImage 机制来处理

  // 如果是当前图片，需要额外的UI状态清理
  if (isCurrentImage) {
    cleanupActions.push(() => {
      // 这些状态清理将在 useImageEditor 中处理
      console.log(`正在清理当前选中图片 ${imageId} 的相关UI状态`);
    });
  }

  return {
    shouldWarnUser,
    warningMessage,
    cleanupActions,
  };
}
